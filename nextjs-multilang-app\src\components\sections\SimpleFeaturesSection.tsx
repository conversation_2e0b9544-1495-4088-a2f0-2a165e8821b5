"use client";

import { motion } from "framer-motion";

export default function SimpleFeaturesSection() {
  return (
    <section className="features">
      <div className="container">
        <motion.div
          className="section-header"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2>How PersonaRoll Works</h2>
          <p>Three simple steps to viral content creation</p>
        </motion.div>

        <div className="features-grid">
          <motion.div
            className="feature-card"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
          >
            <div className="feature-icon">
              <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
                <rect width="40" height="40" rx="12" fill="url(#uploadGrad)" />
                <path
                  d="M20 12v16m-6-10l6-6 6 6"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <defs>
                  <linearGradient id="uploadGrad" x1="0" y1="0" x2="40" y2="40">
                    <stop stopColor="#ff2e4d" />
                    <stop offset="1" stopColor="#ff6b7a" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
            <h3>Upload Your Photos</h3>
            <p>
              Simply upload your camera roll and let our AI analyze your
              lifestyle, interests, and aesthetic preferences.
            </p>
          </motion.div>

          <motion.div
            className="feature-card"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <div className="feature-icon">
              <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
                <rect width="40" height="40" rx="12" fill="url(#aiGrad)" />
                <circle cx="20" cy="16" r="4" stroke="white" strokeWidth="2" />
                <path
                  d="M12 30c0-4.4 3.6-8 8-8s8 3.6 8 8"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
                <defs>
                  <linearGradient id="aiGrad" x1="0" y1="0" x2="40" y2="40">
                    <stop stopColor="#7c3aed" />
                    <stop offset="1" stopColor="#a855f7" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
            <h3>AI Creates Your Personas</h3>
            <p>
              Our AI generates unique personas that match your style, each with
              distinct voices and content preferences.
            </p>
          </motion.div>

          <motion.div
            className="feature-card"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <div className="feature-icon">
              <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
                <rect width="40" height="40" rx="12" fill="url(#contentGrad)" />
                <path
                  d="M12 18h16M12 22h12M12 26h8"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
                <rect
                  x="10"
                  y="12"
                  width="20"
                  height="20"
                  rx="2"
                  stroke="white"
                  strokeWidth="2"
                />
                <defs>
                  <linearGradient
                    id="contentGrad"
                    x1="0"
                    y1="0"
                    x2="40"
                    y2="40"
                  >
                    <stop stopColor="#06b6d4" />
                    <stop offset="1" stopColor="#22d3ee" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
            <h3>Generate Viral Content</h3>
            <p>
              Watch as your personas create authentic, trending content that
              resonates with your audience automatically.
            </p>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
