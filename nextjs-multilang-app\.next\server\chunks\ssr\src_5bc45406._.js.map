{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/i18n/routing.ts"], "sourcesContent": ["import { defineRouting } from \"next-intl/routing\";\nimport { createNavigation } from \"next-intl/navigation\";\n\nexport const routing = defineRouting({\n  // A list of all locales that are supported\n  locales: [\"en\", \"zh\", \"ja\"],\n\n  // Used when no locale matches\n  defaultLocale: \"en\",\n\n  // The `pathnames` object holds pairs of internal and\n  // external paths. Based on the locale, the external\n  // paths are rewritten to the shared, internal ones.\n  pathnames: {\n    // If all locales use the same pathname, a single\n    // external path can be provided for all locales\n    \"/\": \"/\",\n    \"/about\": {\n      en: \"/about\",\n      zh: \"/about\",\n      ja: \"/about\",\n    },\n  },\n});\n\n// Lightweight wrappers around Next.js' navigation APIs\n// that will consider the routing configuration\nexport const { Link, redirect, usePathname, useRouter } =\n  createNavigation(routing);\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,MAAM,UAAU,CAAA,GAAA,kOAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,2CAA2C;IAC3C,SAAS;QAAC;QAAM;QAAM;KAAK;IAE3B,8BAA8B;IAC9B,eAAe;IAEf,qDAAqD;IACrD,oDAAoD;IACpD,oDAAoD;IACpD,WAAW;QACT,iDAAiD;QACjD,gDAAgD;QAChD,KAAK;QACL,UAAU;YACR,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;AACF;AAIO,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,GACrD,CAAA,GAAA,8PAAA,CAAA,mBAAgB,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/i18n/request.ts"], "sourcesContent": ["import { getRequestConfig } from \"next-intl/server\";\nimport { routing } from \"./routing\";\n\nexport default getRequestConfig(async ({ requestLocale }) => {\n  // This typically corresponds to the `[locale]` segment\n  let locale = await requestLocale;\n\n  // Ensure that a valid locale is used\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  if (!locale || !routing.locales.includes(locale as any)) {\n    locale = routing.defaultLocale;\n  }\n\n  return {\n    locale,\n    messages: (await import(`../../messages/${locale}.json`)).default,\n  };\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAEe,CAAA,GAAA,0PAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,aAAa,EAAE;IACtD,uDAAuD;IACvD,IAAI,SAAS,MAAM;IAEnB,qCAAqC;IACrC,8DAA8D;IAC9D,IAAI,CAAC,UAAU,CAAC,sHAAA,CAAA,UAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAgB;QACvD,SAAS,sHAAA,CAAA,UAAO,CAAC,aAAa;IAChC;IAEA,OAAO;QACL;QACA,UAAU,CAAC;;;;;;;;;;;;;kBAAa,CAAC,eAAe,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IACnE;AACF", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/Navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navigation.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navigation.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/Navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navigation.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navigation.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6Q,GAC1S,2CACA", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/app/%5Blocale%5D/layout.tsx"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { NextIntlClientProvider } from \"next-intl\";\nimport { getMessages } from \"next-intl/server\";\nimport { notFound } from \"next/navigation\";\nimport { routing } from \"@/i18n/routing\";\nimport \"../globals.css\";\nimport Navigation from \"@/components/Navigation\";\n\nexport default async function LocaleLayout({\n  children,\n  params,\n}: {\n  children: React.ReactNode;\n  params: Promise<{ locale: string }>;\n}) {\n  const { locale } = await params;\n\n  // Ensure that the incoming `locale` is valid\n  if (!routing.locales.includes(locale as any)) {\n    notFound();\n  }\n\n  // Providing all messages to the client\n  // side is the easiest way to get started\n  const messages = await getMessages();\n\n  return (\n    <html lang={locale}>\n      <body className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n        <NextIntlClientProvider messages={messages}>\n          <Navigation />\n          <main className=\"container mx-auto px-4 py-8\">{children}</main>\n        </NextIntlClientProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AACrD;AACA;AACA;AAAA;AACA;AAEA;;;;;;;;AAEe,eAAe,aAAa,EACzC,QAAQ,EACR,MAAM,EAIP;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IAEzB,6CAA6C;IAC7C,IAAI,CAAC,sHAAA,CAAA,UAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAgB;QAC5C,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,uCAAuC;IACvC,yCAAyC;IACzC,MAAM,WAAW,MAAM,CAAA,GAAA,gPAAA,CAAA,cAAW,AAAD;IAEjC,qBACE,8OAAC;QAAK,MAAM;kBACV,cAAA,8OAAC;YAAK,WAAU;sBACd,cAAA,8OAAC,kQAAA,CAAA,yBAAsB;gBAAC,UAAU;;kCAChC,8OAAC,gIAAA,CAAA,UAAU;;;;;kCACX,8OAAC;wBAAK,WAAU;kCAA+B;;;;;;;;;;;;;;;;;;;;;;AAKzD", "debugId": null}}]}