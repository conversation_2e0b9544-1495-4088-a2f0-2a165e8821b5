"use client";

import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";
import { useTranslations } from "next-intl";

export default function FeaturesSection() {
  const tFeatures = useTranslations("Features");
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.1, 0.35, 1] as const,
      },
    },
  };

  return (
    <section className="py-24 relative bg-bg-secondary w-full" ref={ref}>
      <div className="max-w-6xl mx-auto px-5">
        <div className="text-center mb-20">
          <h2 className="text-4xl lg:text-5xl font-black text-white mb-6 leading-tight">
            {tFeatures("sectionTitle")}
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            {tFeatures("sectionSubtitle")}
          </p>
        </div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto"
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Camera Roll Feature - PRIMARY */}
          <motion.div
            className="bg-gradient-to-br from-red-500/10 to-purple-600/10 border border-red-500/20 rounded-2xl p-8 hover:border-red-500/40 transition-all duration-300"
            variants={cardVariants}
          >
            <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-purple-600 rounded-xl flex items-center justify-center mb-6">
              <div className="relative w-8 h-6">
                <div className="w-full h-full bg-white rounded-sm"></div>
                <div className="absolute top-1 left-1 w-3 h-3 bg-gray-800 rounded-full"></div>
                <div className="absolute top-0 right-0 w-1 h-1 bg-yellow-400 rounded-full"></div>
              </div>
            </div>
            <h3 className="text-xl font-bold text-white mb-4">
              {tFeatures("step1.title")}
            </h3>
            <p className="text-gray-300 mb-6 leading-relaxed">
              {tFeatures("step1.description")}
            </p>
            <ul className="space-y-3">
              <li className="flex items-center text-gray-300 text-sm">
                <span className="w-1.5 h-1.5 bg-red-500 rounded-full mr-3"></span>
                {tFeatures("step1.features.0")}
              </li>
              <li className="flex items-center text-gray-300 text-sm">
                <span className="w-1.5 h-1.5 bg-red-500 rounded-full mr-3"></span>
                {tFeatures("step1.features.1")}
              </li>
              <li className="flex items-center text-gray-300 text-sm">
                <span className="w-1.5 h-1.5 bg-red-500 rounded-full mr-3"></span>
                {tFeatures("step1.features.2")}
              </li>
            </ul>
          </motion.div>

          {/* Persona Feature */}
          <motion.div
            className="bg-gradient-to-br from-purple-500/10 to-blue-600/10 border border-purple-500/20 rounded-2xl p-8 hover:border-purple-500/40 transition-all duration-300"
            variants={cardVariants}
          >
            <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-600 rounded-xl flex items-center justify-center mb-6">
              <div className="flex gap-1">
                <div className="w-2 h-2 bg-white rounded-full"></div>
                <div className="w-2 h-2 bg-white/70 rounded-full"></div>
                <div className="w-2 h-2 bg-white/40 rounded-full"></div>
              </div>
            </div>
            <h3 className="text-xl font-bold text-white mb-4">
              {tFeatures("step2.title")}
            </h3>
            <p className="text-gray-300 mb-6 leading-relaxed">
              {tFeatures("step2.description")}
            </p>
            <ul className="space-y-3">
              <li className="flex items-center text-gray-300 text-sm">
                <span className="w-1.5 h-1.5 bg-purple-500 rounded-full mr-3"></span>
                {tFeatures("step2.features.0")}
              </li>
              <li className="flex items-center text-gray-300 text-sm">
                <span className="w-1.5 h-1.5 bg-purple-500 rounded-full mr-3"></span>
                {tFeatures("step2.features.1")}
              </li>
              <li className="flex items-center text-gray-300 text-sm">
                <span className="w-1.5 h-1.5 bg-purple-500 rounded-full mr-3"></span>
                {tFeatures("step2.features.2")}
              </li>
            </ul>
          </motion.div>

          {/* Trend Detection */}
          <motion.div
            className="bg-gray-800/50 border border-gray-700 rounded-2xl p-8 hover:border-gray-600 transition-all duration-300"
            variants={cardVariants}
          >
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mb-6">
              <div className="relative w-8 h-6">
                <div className="absolute bottom-0 left-0 w-1 h-2 bg-white rounded-sm"></div>
                <div className="absolute bottom-0 left-2 w-1 h-4 bg-white rounded-sm"></div>
                <div className="absolute bottom-0 left-4 w-1 h-6 bg-white rounded-sm"></div>
                <div className="absolute top-1 right-0 w-1 h-1 bg-white rounded-full"></div>
                <div className="absolute top-2 right-1 w-1 h-1 bg-white rounded-full"></div>
                <div className="absolute top-0 right-2 w-1 h-1 bg-white rounded-full"></div>
              </div>
            </div>
            <h3 className="text-xl font-bold text-white mb-4">
              {tFeatures("step3.title")}
            </h3>
            <p className="text-gray-300 mb-6 leading-relaxed">
              {tFeatures("step3.description")}
            </p>
          </motion.div>

          {/* AI Knowledge Integration */}
          <motion.div
            className="bg-gray-800/50 border border-gray-700 rounded-2xl p-8 hover:border-gray-600 transition-all duration-300"
            variants={cardVariants}
          >
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-xl flex items-center justify-center mb-6">
              <div className="relative w-8 h-8">
                <div className="absolute top-0 left-0 w-2 h-2 bg-white rounded-sm"></div>
                <div className="absolute top-0 right-0 w-2 h-2 bg-white rounded-sm"></div>
                <div className="absolute bottom-0 left-0 w-2 h-2 bg-white rounded-sm"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-white rounded-full"></div>
              </div>
            </div>
            <h3 className="text-xl font-bold text-white mb-4">
              {tFeatures("smartKnowledge.title")}
            </h3>
            <p className="text-gray-300 mb-6 leading-relaxed">
              {tFeatures("smartKnowledge.description")}
            </p>
          </motion.div>

          {/* Trending Topics */}
          <motion.div
            className="bg-gray-800/50 border border-gray-700 rounded-2xl p-8 hover:border-gray-600 transition-all duration-300"
            variants={cardVariants}
          >
            <div className="w-16 h-16 bg-gradient-to-br from-pink-500 to-rose-600 rounded-xl flex items-center justify-center mb-6">
              <div className="relative w-8 h-8">
                <div className="absolute top-1 left-1 w-2 h-2 bg-white rounded-full animate-pulse"></div>
                <div className="absolute top-2 right-1 w-1.5 h-1.5 bg-white rounded-full animate-pulse delay-100"></div>
                <div className="absolute bottom-1 left-2 w-1 h-1 bg-white rounded-full animate-pulse delay-200"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-white/20 rounded-full animate-ping"></div>
              </div>
            </div>
            <h3 className="text-xl font-bold text-white mb-4">
              {tFeatures("alwaysOnTrend.title")}
            </h3>
            <p className="text-gray-300 mb-6 leading-relaxed">
              {tFeatures("alwaysOnTrend.description")}
            </p>
          </motion.div>

          {/* Analytics */}
          <motion.div
            className="bg-gray-800/50 border border-gray-700 rounded-2xl p-8 hover:border-gray-600 transition-all duration-300"
            variants={cardVariants}
          >
            <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-amber-600 rounded-xl flex items-center justify-center mb-6">
              <div className="relative w-8 h-6">
                <div className="absolute bottom-0 left-0 w-1 h-2 bg-white rounded-sm"></div>
                <div className="absolute bottom-0 left-2 w-1 h-4 bg-white rounded-sm"></div>
                <div className="absolute bottom-0 left-4 w-1 h-3 bg-white rounded-sm"></div>
                <div className="absolute bottom-0 right-0 w-1 h-5 bg-white rounded-sm"></div>
                <div className="absolute top-0 left-0 w-full h-0.5 bg-white rounded-full"></div>
              </div>
            </div>
            <h3 className="text-xl font-bold text-white mb-4">
              {tFeatures("performanceInsights.title")}
            </h3>
            <p className="text-gray-300 mb-6 leading-relaxed">
              {tFeatures("performanceInsights.description")}
            </p>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
