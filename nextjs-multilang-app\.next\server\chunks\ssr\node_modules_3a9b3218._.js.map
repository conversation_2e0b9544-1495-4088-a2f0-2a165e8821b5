{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/node_modules/next-intl/dist/esm/development/react-client/index.js"], "sourcesContent": ["import { useFormatter as useFormatter$1, useTranslations as useTranslations$1 } from 'use-intl';\nexport * from 'use-intl';\n\n/**\n * This is the main entry file when non-'react-server'\n * environments import from 'next-intl'.\n *\n * Maintainer notes:\n * - Make sure this mirrors the API from 'react-server'.\n * - Make sure everything exported from this module is\n *   supported in all Next.js versions that are supported.\n */\n\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nfunction callHook(name, hook) {\n  return (...args) => {\n    try {\n      return hook(...args);\n    } catch {\n      throw new Error(`Failed to call \\`${name}\\` because the context from \\`NextIntlClientProvider\\` was not found.\n\nThis can happen because:\n1) You intended to render this component as a Server Component, the render\n   failed, and therefore <PERSON><PERSON> attempted to render the component on the client\n   instead. If this is the case, check the console for server errors.\n2) You intended to render this component on the client side, but no context was found.\n   Learn more about this error here: https://next-intl.dev/docs/environments/server-client-components#missing-context` );\n    }\n  };\n}\nconst useTranslations = callHook('useTranslations', useTranslations$1);\nconst useFormatter = callHook('useFormatter', useFormatter$1);\n\nexport { useFormatter, useTranslations };\n"], "names": [], "mappings": ";;;;AAAA;;;AAGA;;;;;;;;CAQC,GAGD,sEAAsE;AACtE,SAAS,SAAS,IAAI,EAAE,IAAI;IAC1B,OAAO,CAAC,GAAG;QACT,IAAI;YACF,OAAO,QAAQ;QACjB,EAAE,OAAM;YACN,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,KAAK;;;;;;;qHAOsE,CAAC;QAClH;IACF;AACF;AACA,MAAM,kBAAkB,SAAS,mBAAmB,kKAAA,CAAA,kBAAiB;AACrE,MAAM,eAAe,SAAS,gBAAgB,kKAAA,CAAA,eAAc", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs"], "sourcesContent": ["import { resolveElements } from 'motion-dom';\n\nconst thresholds = {\n    some: 0,\n    all: 1,\n};\nfunction inView(elementOrSelector, onStart, { root, margin: rootMargin, amount = \"some\" } = {}) {\n    const elements = resolveElements(elementOrSelector);\n    const activeIntersections = new WeakMap();\n    const onIntersectionChange = (entries) => {\n        entries.forEach((entry) => {\n            const onEnd = activeIntersections.get(entry.target);\n            /**\n             * If there's no change to the intersection, we don't need to\n             * do anything here.\n             */\n            if (entry.isIntersecting === Boolean(onEnd))\n                return;\n            if (entry.isIntersecting) {\n                const newOnEnd = onStart(entry.target, entry);\n                if (typeof newOnEnd === \"function\") {\n                    activeIntersections.set(entry.target, newOnEnd);\n                }\n                else {\n                    observer.unobserve(entry.target);\n                }\n            }\n            else if (typeof onEnd === \"function\") {\n                onEnd(entry);\n                activeIntersections.delete(entry.target);\n            }\n        });\n    };\n    const observer = new IntersectionObserver(onIntersectionChange, {\n        root,\n        rootMargin,\n        threshold: typeof amount === \"number\" ? amount : thresholds[amount],\n    });\n    elements.forEach((element) => observer.observe(element));\n    return () => observer.disconnect();\n}\n\nexport { inView };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,aAAa;IACf,MAAM;IACN,KAAK;AACT;AACA,SAAS,OAAO,iBAAiB,EAAE,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,UAAU,EAAE,SAAS,MAAM,EAAE,GAAG,CAAC,CAAC;IAC1F,MAAM,WAAW,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE;IACjC,MAAM,sBAAsB,IAAI;IAChC,MAAM,uBAAuB,CAAC;QAC1B,QAAQ,OAAO,CAAC,CAAC;YACb,MAAM,QAAQ,oBAAoB,GAAG,CAAC,MAAM,MAAM;YAClD;;;aAGC,GACD,IAAI,MAAM,cAAc,KAAK,QAAQ,QACjC;YACJ,IAAI,MAAM,cAAc,EAAE;gBACtB,MAAM,WAAW,QAAQ,MAAM,MAAM,EAAE;gBACvC,IAAI,OAAO,aAAa,YAAY;oBAChC,oBAAoB,GAAG,CAAC,MAAM,MAAM,EAAE;gBAC1C,OACK;oBACD,SAAS,SAAS,CAAC,MAAM,MAAM;gBACnC;YACJ,OACK,IAAI,OAAO,UAAU,YAAY;gBAClC,MAAM;gBACN,oBAAoB,MAAM,CAAC,MAAM,MAAM;YAC3C;QACJ;IACJ;IACA,MAAM,WAAW,IAAI,qBAAqB,sBAAsB;QAC5D;QACA;QACA,WAAW,OAAO,WAAW,WAAW,SAAS,UAAU,CAAC,OAAO;IACvE;IACA,SAAS,OAAO,CAAC,CAAC,UAAY,SAAS,OAAO,CAAC;IAC/C,OAAO,IAAM,SAAS,UAAU;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/node_modules/framer-motion/dist/es/utils/use-in-view.mjs"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { inView } from '../render/dom/viewport/index.mjs';\n\nfunction useInView(ref, { root, margin, amount, once = false, initial = false, } = {}) {\n    const [isInView, setInView] = useState(initial);\n    useEffect(() => {\n        if (!ref.current || (once && isInView))\n            return;\n        const onEnter = () => {\n            setInView(true);\n            return once ? undefined : () => setInView(false);\n        };\n        const options = {\n            root: (root && root.current) || undefined,\n            margin,\n            amount,\n        };\n        return inView(ref.current, onEnter, options);\n    }, [root, ref, margin, once, amount]);\n    return isInView;\n}\n\nexport { useInView };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,UAAU,GAAG,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,KAAK,EAAE,UAAU,KAAK,EAAG,GAAG,CAAC,CAAC;IACjF,MAAM,CAAC,UAAU,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,IAAI,OAAO,IAAK,QAAQ,UACzB;QACJ,MAAM,UAAU;YACZ,UAAU;YACV,OAAO,OAAO,YAAY,IAAM,UAAU;QAC9C;QACA,MAAM,UAAU;YACZ,MAAM,AAAC,QAAQ,KAAK,OAAO,IAAK;YAChC;YACA;QACJ;QACA,OAAO,CAAA,GAAA,qLAAA,CAAA,SAAM,AAAD,EAAE,IAAI,OAAO,EAAE,SAAS;IACxC,GAAG;QAAC;QAAM;QAAK;QAAQ;QAAM;KAAO;IACpC,OAAO;AACX", "ignoreList": [0], "debugId": null}}]}