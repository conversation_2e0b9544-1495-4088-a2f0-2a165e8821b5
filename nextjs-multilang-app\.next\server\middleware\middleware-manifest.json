{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_cd76b067._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_75a74226._.js", "server/edge/chunks/[root-of-the-server]__47f0086d._.js", "server/edge/chunks/edge-wrapper_be163fe5.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(zh|en|ja)/:path*{(\\\\.json)}?", "originalSource": "/(zh|en|ja)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "CPMI/zEbCknzvIHZWmaMvRokN9Bs87M7zmty+a43oIE=", "__NEXT_PREVIEW_MODE_ID": "378a4f62e9c885417478839bf485cfbb", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2336f75113d7e74c9e1989caf150546739deb4eb85462b3047c4670c0ed13e5e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "71486bc203f6b3842cd1de922ad56eb1f04d5138515c214de49dfaef6960132d"}}}, "instrumentation": null, "functions": {}}