"use client";

import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useState } from "react";
import { useTranslations } from "next-intl";

export default function CTASection() {
  const tCTA = useTranslations("CTA");
  const tCommon = useTranslations("Common");
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [email, setEmail] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (email) {
      // Simulate form submission
      setIsSubmitted(true);
      setEmail("");
    }
  };

  const containerVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut" as const,
      },
    },
  };

  return (
    <section className="py-24 text-center relative bg-bg-secondary" ref={ref}>
      <div className="max-w-4xl mx-auto px-5">
        <motion.div
          className="space-y-8"
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          <h2 className="text-4xl lg:text-5xl font-black text-white leading-tight">
            {tCTA("title")}
          </h2>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed">
            {tCTA("subtitle")}
          </p>

          {!isSubmitted ? (
            <form className="mt-12" onSubmit={handleSubmit}>
              <div className="flex flex-col sm:flex-row gap-4 max-w-lg mx-auto">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder={tCTA("emailPlaceholder")}
                  className="flex-1 px-6 py-4 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500 focus:ring-2 focus:ring-red-500/20 transition-all duration-300"
                  required
                />
                <button
                  type="submit"
                  className="bg-gradient-to-r from-red-500 to-purple-600 text-white px-8 py-4 rounded-lg font-semibold text-base hover:transform hover:-translate-y-1 transition-all duration-300 shadow-lg hover:shadow-red-500/30 whitespace-nowrap"
                >
                  {tCommon("getEarlyAccess")}
                </button>
              </div>
              <p className="text-gray-400 text-sm mt-6 max-w-md mx-auto">
                {tCTA("note")}
              </p>
            </form>
          ) : (
            <div className="bg-green-500/10 border border-green-500/20 rounded-xl p-8 max-w-md mx-auto">
              <h3 className="text-xl font-bold text-green-400 mb-3">
                {tCTA("successTitle")}
              </h3>
              <p className="text-gray-300">{tCTA("successMessage")}</p>
            </div>
          )}
        </motion.div>
      </div>
    </section>
  );
}
