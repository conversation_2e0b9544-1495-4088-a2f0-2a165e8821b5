"use client";

import { motion } from "framer-motion";
import { useState } from "react";

export default function SimpleCTASection() {
  const [email, setEmail] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (email) {
      setIsSubmitted(true);
      setTimeout(() => {
        setIsSubmitted(false);
        setEmail("");
      }, 3000);
    }
  };

  return (
    <section className="cta">
      <div className="container">
        <motion.div
          className="cta-content"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2>Ready to Go Viral?</h2>
          <p>
            Join thousands of creators who are already using PersonaRoll to
            create authentic, trending content automatically.
          </p>

          {!isSubmitted ? (
            <form onSubmit={handleSubmit} className="cta-form">
              <div className="form-group">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  required
                  className="email-input"
                />
                <button type="submit" className="btn-primary btn-large">
                  Get Early Access
                </button>
              </div>
              <p className="form-note">
                🎉 Early access includes lifetime 50% discount
              </p>
            </form>
          ) : (
            <motion.div
              className="success-message"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="success-icon">✨</div>
              <h3>You&apos;re on the list!</h3>
              <p>We&apos;ll notify you as soon as PersonaRoll is ready.</p>
            </motion.div>
          )}
        </motion.div>
      </div>
    </section>
  );
}
