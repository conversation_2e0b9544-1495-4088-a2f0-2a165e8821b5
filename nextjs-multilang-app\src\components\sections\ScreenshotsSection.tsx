"use client";

import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";
import { useTranslations } from "next-intl";

export default function ScreenshotsSection() {
  const tScreenshots = useTranslations("Screenshots");
  const tContentGeneration = useTranslations("ContentGeneration");
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
      },
    },
  };

  return (
    <section className="py-24 relative" ref={ref}>
      <div className="max-w-6xl mx-auto px-5">
        <div className="text-center mb-20">
          <h2 className="text-4xl lg:text-5xl font-black text-white mb-6 leading-tight">
            {tScreenshots("sectionTitle")}
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            {tScreenshots("sectionSubtitle")}
          </p>
        </div>

        <motion.div
          className="space-y-20"
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Screenshot 1: Photo Upload */}
          <motion.div
            className="flex flex-col lg:flex-row items-center gap-12"
            variants={itemVariants}
          >
            <div className="flex-1 rounded-2xl">
              <div className="bg-bg-secondary border border-border rounded-xl">
                {/* Header with title and filter tabs */}
                <div className="mb-6 bg-bg-primary">
                  <div className="mb-4 pt-6 pl-6">
                    <h4 className="text-xl font-bold text-white mb-2">
                      Camera Roll Analyzer
                    </h4>
                    <span className="text-sm text-gray-400">
                      AI analyzes photos for trending content
                    </span>
                  </div>
                  <div className="flex gap-2 flex-wrap pl-6 pb-4">
                    <div className="px-3 py-1.5 bg-red-500 text-white text-xs font-medium rounded-lg">
                      All (24)
                    </div>
                    <div className="px-3 py-1.5 bg-bg-secondary text-gray-300 text-xs font-medium rounded-lg  border-1 border-border hover:bg-bg-tertiary cursor-pointer">
                      Fashion (8)
                    </div>
                    <div className="px-3 py-1.5 bg-bg-secondary  text-gray-300 text-xs font-medium border-1 border-border rounded-lg hover:bg-bg-tertiary cursor-pointer">
                      Lifestyle (6)
                    </div>
                    <div className="px-3 py-1.5 bg-bg-secondary text-gray-300 text-xs font-medium border-1 border-border rounded-lg hover:bg-bg-tertiary cursor-pointer">
                      Food (4)
                    </div>
                    <div className="px-3 py-1.5 bg-bg-secondary  text-gray-300 text-xs font-medium border-1 border-border rounded-lg hover:bg-bg-tertiary cursor-pointer">
                      Travel (6)
                    </div>
                  </div>
                </div>

                {/* Photo Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-6 pt-0">
                  <div className="bg-bg-secondary rounded-lg border-1 border-[#ff6b7a]">
                    <div className="relative mb-3">
                      <div
                        className="w-full h-24 rounded-ss-lg rounded-se-lg text-2xl flex justify-center items-center"
                        style={{
                          background:
                            "linear-gradient(135deg, #ff6b7a 0%, #c44569 100%)",
                        }}
                      >
                        👗
                      </div>
                    </div>
                    <div className="space-y-2 p-3">
                      <div className="text-text-secondary font-medium text-sm">
                        autumn_outfit_2024.jpg
                      </div>
                      <div className="text-text-muted text-xs leading-relaxed line-clamp-2">
                        Cozy autumn sweater with matching accessories, perfect
                        for fall weather trends
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-text-muted text-xs">
                          Oct 28, 2024
                        </span>
                        <span
                          style={{
                            background: "rgba(255, 46, 77, 0.15)",
                          }}
                          className="px-2 py-1  text-accent-red text-xs rounded-full"
                        >
                          Used
                        </span>
                      </div>
                      <div className="flex gap-1 flex-wrap">
                        <span className="px-2 py-1 bg-bg-tertiary text-text-secondary text-xs rounded-lg">
                          autumn
                        </span>
                        <span className="px-2 py-1 bg-bg-tertiary text-text-secondary text-xs rounded-lg">
                          fashion
                        </span>
                        <span className="px-2 py-1 bg-bg-tertiary text-text-secondary  text-xs rounded-lg">
                          cozy
                        </span>
                      </div>
                      <div className="inline-block px-2 py-1 bg-accent-red mt-1 text-white text-xs rounded-lg">
                        Fashion
                      </div>
                    </div>
                  </div>

                  <div className="bg-bg-secondary rounded-lg pb-4 border border-gray-600 hover:border-gray-500 transition-colors">
                    <div className="relative mb-3">
                      <div
                        className="w-full h-24  rounded-ss-lg rounded-se-lg text-2xl flex justify-center items-center"
                        style={{
                          background:
                            "linear-gradient(135deg, #4834d4 0%, #686de0 100%)",
                        }}
                      >
                        ☕
                      </div>
                    </div>
                    <div className="space-y-2 p-3">
                      <div className="text-text-secondary font-medium text-sm">
                        morning_coffee.jpg
                      </div>
                      <div className="text-text-muted text-xs leading-relaxed">
                        Perfect morning coffee setup with natural lighting
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-text-muted text-xs">
                          Oct 27, 2024
                        </span>
                        <span className="px-2 py-1 bg-bg-tertiary text-white text-xs rounded-full">
                          Unused
                        </span>
                      </div>
                      <div className="flex gap-1 flex-wrap">
                        <span className="px-2 py-1 bg-bg-tertiary text-gray-200 text-xs rounded-full">
                          coffee
                        </span>
                        <span className="px-2 py-1 bg-bg-tertiary  text-gray-200 text-xs rounded-full">
                          morning
                        </span>
                      </div>
                      <div className="inline-block px-2 py-1  bg-accent-red mt-1 text-white text-xs rounded-full">
                        Lifestyle
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex-1 space-y-6">
              <h3 className="text-2xl font-bold text-white">
                Start with Your Photos
              </h3>
              <p className="text-gray-300 leading-relaxed">
                Upload any photo and watch AI instantly match it with trending
                topics. Your visuals drive the content, not the other way
                around.
              </p>
            </div>
          </motion.div>

          {/* Screenshot 2: Persona Selection */}
          <motion.div
            className="screenshot-item reverse"
            variants={itemVariants}
          >
            <div className="screenshot-image">
              <div className="screenshot-content">
                <div className="demo-persona-manager-compact">
                  <div className="persona-grid">
                    <div className="persona-card active">
                      <div className="persona-card-avatar fiona">F</div>
                      <h4>Fashion Fiona</h4>
                      <p>ENFP • Fashion & Lifestyle</p>
                      <div className="persona-knowledge-preview">
                        <div className="knowledge-chip-mini active">
                          Fashion
                        </div>
                        <div className="knowledge-chip-mini active">Style</div>
                        <div className="knowledge-chip-mini active">Trends</div>
                      </div>
                    </div>

                    <div className="persona-card">
                      <div className="persona-card-avatar marcus">M</div>
                      <h4>Chef Marcus</h4>
                      <p>ISFJ • Culinary Expert</p>
                      <div className="persona-knowledge-preview">
                        <div className="knowledge-chip-mini">Recipes</div>
                        <div className="knowledge-chip-mini">Cooking</div>
                        <div className="knowledge-chip-mini">Food</div>
                      </div>
                    </div>
                  </div>

                  <div className="persona-detail-summary">
                    <h4
                      style={{
                        color: "var(--text-primary)",
                        marginBottom: "12px",
                        fontSize: "16px",
                      }}
                    >
                      Selected: Fashion Fiona
                    </h4>
                    <p
                      style={{
                        color: "var(--text-secondary)",
                        fontSize: "14px",
                        lineHeight: "1.5",
                        marginBottom: "16px",
                      }}
                    >
                      A fashion-forward influencer who stays ahead of trends and
                      creates authentic style content. Reads fashion feeds and
                      writes with trendy enthusiasm.
                    </p>
                    <div className="knowledge-sources-summary">
                      <div className="knowledge-chips">
                        <div className="knowledge-chip active">Vogue RSS</div>
                        <div className="knowledge-chip active">
                          Fashion Week
                        </div>
                        <div className="knowledge-chip active">Style Blogs</div>
                        <div className="knowledge-chip">Beauty Tips</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="screenshot-info">
              <h3>Personas with Custom Knowledge</h3>
              <p>
                Each persona reads different sources and writes in their unique
                style. Fashion Fiona follows fashion feeds, while Foodie Fred
                reads culinary blogs.
              </p>
            </div>
          </motion.div>

          {/* Screenshot 3: AI Generation Process */}
          <motion.div className="screenshot-item" variants={itemVariants}>
            <div className="screenshot-image">
              <div className="screenshot-content">
                <div className="demo-content-generation-compact">
                  <div className="generation-process">
                    <div className="process-steps">
                      <div className="step-item active">
                        <div className="step-icon">📸</div>
                        <span>{tContentGeneration("steps.photoAnalysis")}</span>
                      </div>
                      <div className="step-arrow">→</div>
                      <div className="step-item active">
                        <div className="step-icon">📈</div>
                        <span>{tContentGeneration("steps.trendMatch")}</span>
                      </div>
                      <div className="step-arrow">→</div>
                      <div className="step-item active">
                        <div className="step-icon">✨</div>
                        <span>{tContentGeneration("steps.generate")}</span>
                      </div>
                    </div>
                  </div>

                  <div className="generated-content-preview bg-bg-secondary">
                    <div className="content-example">
                      <div className="content-header">
                        <div className="persona-badge">
                          {tContentGeneration("badges.fashionFiona")}
                        </div>
                        <div className="trend-badge">
                          {tContentGeneration("badges.autumnTrends")}
                        </div>
                      </div>
                      <div className="content-body">
                        <p>{tContentGeneration("sampleContent")}</p>
                      </div>
                      <div className="content-platforms">
                        <span className="platform-icon">📱</span>
                        <span className="platform-icon">📷</span>
                        <span className="platform-icon">🐦</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="screenshot-info">
              <h3>{tScreenshots("smartContentGeneration.title")}</h3>
              <p>{tScreenshots("smartContentGeneration.description")}</p>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
