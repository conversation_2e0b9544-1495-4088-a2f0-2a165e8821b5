{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/sections/FeaturesSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"framer-motion\";\nimport { useRef } from \"react\";\nimport { useTranslations } from \"next-intl\";\n\nexport default function FeaturesSection() {\n  const tFeatures = useTranslations(\"Features\");\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" });\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n      },\n    },\n  };\n\n  const cardVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        ease: [0.25, 0.1, 0.35, 1] as const,\n      },\n    },\n  };\n\n  return (\n    <section className=\"py-24 relative bg-gray-900\" ref={ref}>\n      <div className=\"max-w-6xl mx-auto px-5\">\n        <div className=\"text-center mb-20\">\n          <h2 className=\"text-4xl lg:text-5xl font-black text-white mb-6 leading-tight\">\n            {tFeatures(\"sectionTitle\")}\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\">\n            {tFeatures(\"sectionSubtitle\")}\n          </p>\n        </div>\n\n        <motion.div\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate={isInView ? \"visible\" : \"hidden\"}\n        >\n          {/* Camera Roll Feature - PRIMARY */}\n          <motion.div\n            className=\"bg-gradient-to-br from-red-500/10 to-purple-600/10 border border-red-500/20 rounded-2xl p-8 hover:border-red-500/40 transition-all duration-300\"\n            variants={cardVariants}\n          >\n            <div className=\"w-16 h-16 bg-gradient-to-br from-red-500 to-purple-600 rounded-xl flex items-center justify-center mb-6\">\n              <div className=\"relative w-8 h-6\">\n                <div className=\"w-full h-full bg-white rounded-sm\"></div>\n                <div className=\"absolute top-1 left-1 w-3 h-3 bg-gray-800 rounded-full\"></div>\n                <div className=\"absolute top-0 right-0 w-1 h-1 bg-yellow-400 rounded-full\"></div>\n              </div>\n            </div>\n            <h3 className=\"text-xl font-bold text-white mb-4\">\n              {tFeatures(\"step1.title\")}\n            </h3>\n            <p className=\"text-gray-300 mb-6 leading-relaxed\">\n              {tFeatures(\"step1.description\")}\n            </p>\n            <ul className=\"space-y-3\">\n              <li className=\"flex items-center text-gray-300 text-sm\">\n                <span className=\"w-1.5 h-1.5 bg-red-500 rounded-full mr-3\"></span>\n                {tFeatures(\"step1.features.0\")}\n              </li>\n              <li className=\"flex items-center text-gray-300 text-sm\">\n                <span className=\"w-1.5 h-1.5 bg-red-500 rounded-full mr-3\"></span>\n                {tFeatures(\"step1.features.1\")}\n              </li>\n              <li className=\"flex items-center text-gray-300 text-sm\">\n                <span className=\"w-1.5 h-1.5 bg-red-500 rounded-full mr-3\"></span>\n                {tFeatures(\"step1.features.2\")}\n              </li>\n            </ul>\n          </motion.div>\n\n          {/* Persona Feature */}\n          <motion.div\n            className=\"bg-gradient-to-br from-purple-500/10 to-blue-600/10 border border-purple-500/20 rounded-2xl p-8 hover:border-purple-500/40 transition-all duration-300\"\n            variants={cardVariants}\n          >\n            <div className=\"w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-600 rounded-xl flex items-center justify-center mb-6\">\n              <div className=\"flex gap-1\">\n                <div className=\"w-2 h-2 bg-white rounded-full\"></div>\n                <div className=\"w-2 h-2 bg-white/70 rounded-full\"></div>\n                <div className=\"w-2 h-2 bg-white/40 rounded-full\"></div>\n              </div>\n            </div>\n            <h3 className=\"text-xl font-bold text-white mb-4\">\n              {tFeatures(\"step2.title\")}\n            </h3>\n            <p className=\"text-gray-300 mb-6 leading-relaxed\">\n              {tFeatures(\"step2.description\")}\n            </p>\n            <ul className=\"space-y-3\">\n              <li className=\"flex items-center text-gray-300 text-sm\">\n                <span className=\"w-1.5 h-1.5 bg-purple-500 rounded-full mr-3\"></span>\n                {tFeatures(\"step2.features.0\")}\n              </li>\n              <li className=\"flex items-center text-gray-300 text-sm\">\n                <span className=\"w-1.5 h-1.5 bg-purple-500 rounded-full mr-3\"></span>\n                {tFeatures(\"step2.features.1\")}\n              </li>\n              <li className=\"flex items-center text-gray-300 text-sm\">\n                <span className=\"w-1.5 h-1.5 bg-purple-500 rounded-full mr-3\"></span>\n                {tFeatures(\"step2.features.2\")}\n              </li>\n            </ul>\n          </motion.div>\n\n          {/* Trend Detection */}\n          <motion.div\n            className=\"bg-gray-800/50 border border-gray-700 rounded-2xl p-8 hover:border-gray-600 transition-all duration-300\"\n            variants={cardVariants}\n          >\n            <div className=\"w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mb-6\">\n              <div className=\"relative w-8 h-6\">\n                <div className=\"absolute bottom-0 left-0 w-1 h-2 bg-white rounded-sm\"></div>\n                <div className=\"absolute bottom-0 left-2 w-1 h-4 bg-white rounded-sm\"></div>\n                <div className=\"absolute bottom-0 left-4 w-1 h-6 bg-white rounded-sm\"></div>\n                <div className=\"absolute top-1 right-0 w-1 h-1 bg-white rounded-full\"></div>\n                <div className=\"absolute top-2 right-1 w-1 h-1 bg-white rounded-full\"></div>\n                <div className=\"absolute top-0 right-2 w-1 h-1 bg-white rounded-full\"></div>\n              </div>\n            </div>\n            <h3 className=\"text-xl font-bold text-white mb-4\">\n              {tFeatures(\"step3.title\")}\n            </h3>\n            <p className=\"text-gray-300 mb-6 leading-relaxed\">\n              {tFeatures(\"step3.description\")}\n            </p>\n          </motion.div>\n\n          {/* AI Knowledge Integration */}\n          <motion.div\n            className=\"bg-gray-800/50 border border-gray-700 rounded-2xl p-8 hover:border-gray-600 transition-all duration-300\"\n            variants={cardVariants}\n          >\n            <div className=\"w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-xl flex items-center justify-center mb-6\">\n              <div className=\"relative w-8 h-8\">\n                <div className=\"absolute top-0 left-0 w-2 h-2 bg-white rounded-sm\"></div>\n                <div className=\"absolute top-0 right-0 w-2 h-2 bg-white rounded-sm\"></div>\n                <div className=\"absolute bottom-0 left-0 w-2 h-2 bg-white rounded-sm\"></div>\n                <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-white rounded-full\"></div>\n              </div>\n            </div>\n            <h3 className=\"text-xl font-bold text-white mb-4\">\n              {tFeatures(\"smartKnowledge.title\")}\n            </h3>\n            <p className=\"text-gray-300 mb-6 leading-relaxed\">\n              {tFeatures(\"smartKnowledge.description\")}\n            </p>\n          </motion.div>\n\n          {/* Trending Topics */}\n          <motion.div\n            className=\"bg-gray-800/50 border border-gray-700 rounded-2xl p-8 hover:border-gray-600 transition-all duration-300\"\n            variants={cardVariants}\n          >\n            <div className=\"w-16 h-16 bg-gradient-to-br from-pink-500 to-rose-600 rounded-xl flex items-center justify-center mb-6\">\n              <div className=\"relative w-8 h-8\">\n                <div className=\"absolute top-1 left-1 w-2 h-2 bg-white rounded-full animate-pulse\"></div>\n                <div className=\"absolute top-2 right-1 w-1.5 h-1.5 bg-white rounded-full animate-pulse delay-100\"></div>\n                <div className=\"absolute bottom-1 left-2 w-1 h-1 bg-white rounded-full animate-pulse delay-200\"></div>\n                <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-white/20 rounded-full animate-ping\"></div>\n              </div>\n            </div>\n            <h3 className=\"text-xl font-bold text-white mb-4\">\n              {tFeatures(\"alwaysOnTrend.title\")}\n            </h3>\n            <p className=\"text-gray-300 mb-6 leading-relaxed\">\n              {tFeatures(\"alwaysOnTrend.description\")}\n            </p>\n          </motion.div>\n\n          {/* Analytics */}\n          <motion.div\n            className=\"bg-gray-800/50 border border-gray-700 rounded-2xl p-8 hover:border-gray-600 transition-all duration-300\"\n            variants={cardVariants}\n          >\n            <div className=\"w-16 h-16 bg-gradient-to-br from-orange-500 to-amber-600 rounded-xl flex items-center justify-center mb-6\">\n              <div className=\"relative w-8 h-6\">\n                <div className=\"absolute bottom-0 left-0 w-1 h-2 bg-white rounded-sm\"></div>\n                <div className=\"absolute bottom-0 left-2 w-1 h-4 bg-white rounded-sm\"></div>\n                <div className=\"absolute bottom-0 left-4 w-1 h-3 bg-white rounded-sm\"></div>\n                <div className=\"absolute bottom-0 right-0 w-1 h-5 bg-white rounded-sm\"></div>\n                <div className=\"absolute top-0 left-0 w-full h-0.5 bg-white rounded-full\"></div>\n              </div>\n            </div>\n            <h3 className=\"text-xl font-bold text-white mb-4\">\n              {tFeatures(\"performanceInsights.title\")}\n            </h3>\n            <p className=\"text-gray-300 mb-6 leading-relaxed\">\n              {tFeatures(\"performanceInsights.description\")}\n            </p>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,YAAY,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAClC,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAE/D,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBAC<PERSON>,MAAM;oBAAC;oBAAM;oBAAK;oBAAM;iBAAE;YAC5B;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;QAA6B,KAAK;kBACnD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,UAAU;;;;;;sCAEb,8OAAC;4BAAE,WAAU;sCACV,UAAU;;;;;;;;;;;;8BAIf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU;oBACV,SAAQ;oBACR,SAAS,WAAW,YAAY;;sCAGhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAGnB,8OAAC;oCAAG,WAAU;8CACX,UAAU;;;;;;8CAEb,8OAAC;oCAAE,WAAU;8CACV,UAAU;;;;;;8CAEb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;;;;;;gDACf,UAAU;;;;;;;sDAEb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;;;;;;gDACf,UAAU;;;;;;;sDAEb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;;;;;;gDACf,UAAU;;;;;;;;;;;;;;;;;;;sCAMjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAGnB,8OAAC;oCAAG,WAAU;8CACX,UAAU;;;;;;8CAEb,8OAAC;oCAAE,WAAU;8CACV,UAAU;;;;;;8CAEb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;;;;;;gDACf,UAAU;;;;;;;sDAEb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;;;;;;gDACf,UAAU;;;;;;;sDAEb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;;;;;;gDACf,UAAU;;;;;;;;;;;;;;;;;;;sCAMjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAGnB,8OAAC;oCAAG,WAAU;8CACX,UAAU;;;;;;8CAEb,8OAAC;oCAAE,WAAU;8CACV,UAAU;;;;;;;;;;;;sCAKf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAGnB,8OAAC;oCAAG,WAAU;8CACX,UAAU;;;;;;8CAEb,8OAAC;oCAAE,WAAU;8CACV,UAAU;;;;;;;;;;;;sCAKf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAGnB,8OAAC;oCAAG,WAAU;8CACX,UAAU;;;;;;8CAEb,8OAAC;oCAAE,WAAU;8CACV,UAAU;;;;;;;;;;;;sCAKf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAGnB,8OAAC;oCAAG,WAAU;8CACX,UAAU;;;;;;8CAEb,8OAAC;oCAAE,WAAU;8CACV,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzB", "debugId": null}}, {"offset": {"line": 670, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/sections/ScreenshotsSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"framer-motion\";\nimport { useRef } from \"react\";\nimport { useTranslations } from \"next-intl\";\n\nexport default function ScreenshotsSection() {\n  const tScreenshots = useTranslations(\"Screenshots\");\n  const tContentGeneration = useTranslations(\"ContentGeneration\");\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" });\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.3,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 50 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.8,\n      },\n    },\n  };\n\n  return (\n    <section className=\"py-24 relative\" ref={ref}>\n      <div className=\"max-w-6xl mx-auto px-5\">\n        <div className=\"text-center mb-20\">\n          <h2 className=\"text-4xl lg:text-5xl font-black text-white mb-6 leading-tight\">\n            {tScreenshots(\"sectionTitle\")}\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\">\n            {tScreenshots(\"sectionSubtitle\")}\n          </p>\n        </div>\n\n        <motion.div\n          className=\"space-y-20\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate={isInView ? \"visible\" : \"hidden\"}\n        >\n          {/* Screenshot 1: Photo Upload */}\n          <motion.div\n            className=\"flex flex-col lg:flex-row items-center gap-12\"\n            variants={itemVariants}\n          >\n            <div className=\"flex-1 bg-gray-800 rounded-2xl p-6 border border-gray-700 shadow-2xl\">\n              <div className=\"bg-gray-900 rounded-xl p-6\">\n                {/* Header with title and filter tabs */}\n                <div className=\"mb-6\">\n                  <div className=\"mb-4\">\n                    <h4 className=\"text-xl font-bold text-white mb-2\">\n                      Camera Roll Analyzer\n                    </h4>\n                    <span className=\"text-sm text-gray-400\">\n                      AI analyzes photos for trending content\n                    </span>\n                  </div>\n                  <div className=\"flex gap-2 flex-wrap\">\n                    <div className=\"px-3 py-1.5 bg-red-500 text-white text-xs font-medium rounded-full\">\n                      All (24)\n                    </div>\n                    <div className=\"px-3 py-1.5 bg-gray-700 text-gray-300 text-xs font-medium rounded-full hover:bg-gray-600 cursor-pointer\">\n                      Fashion (8)\n                    </div>\n                    <div className=\"px-3 py-1.5 bg-gray-700 text-gray-300 text-xs font-medium rounded-full hover:bg-gray-600 cursor-pointer\">\n                      Lifestyle (6)\n                    </div>\n                    <div className=\"px-3 py-1.5 bg-gray-700 text-gray-300 text-xs font-medium rounded-full hover:bg-gray-600 cursor-pointer\">\n                      Food (4)\n                    </div>\n                    <div className=\"px-3 py-1.5 bg-gray-700 text-gray-300 text-xs font-medium rounded-full hover:bg-gray-600 cursor-pointer\">\n                      Travel (6)\n                    </div>\n                  </div>\n                </div>\n\n                {/* Photo Grid */}\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div className=\"bg-gray-700 rounded-lg p-4 border-2 border-red-500\">\n                    <div className=\"relative mb-3\">\n                      <div className=\"w-full h-24 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg\"></div>\n                      <div className=\"absolute top-2 right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-white text-xs cursor-pointer hover:bg-red-600\">\n                        🗑️\n                      </div>\n                    </div>\n                    <div className=\"space-y-2\">\n                      <div className=\"text-white font-medium text-sm\">\n                        autumn_outfit_2024.jpg\n                      </div>\n                      <div className=\"text-gray-300 text-xs leading-relaxed\">\n                        Cozy autumn sweater with matching accessories, perfect\n                        for fall weather trends\n                      </div>\n                      <div className=\"flex justify-between items-center\">\n                        <span className=\"text-gray-400 text-xs\">\n                          Oct 28, 2024\n                        </span>\n                        <span className=\"px-2 py-1 bg-green-500 text-white text-xs rounded-full\">\n                          Used\n                        </span>\n                      </div>\n                      <div className=\"flex gap-1 flex-wrap\">\n                        <span className=\"px-2 py-1 bg-gray-600 text-gray-200 text-xs rounded-full\">\n                          autumn\n                        </span>\n                        <span className=\"px-2 py-1 bg-gray-600 text-gray-200 text-xs rounded-full\">\n                          fashion\n                        </span>\n                        <span className=\"px-2 py-1 bg-gray-600 text-gray-200 text-xs rounded-full\">\n                          cozy\n                        </span>\n                      </div>\n                      <div className=\"inline-block px-2 py-1 bg-red-500 text-white text-xs rounded-full\">\n                        Fashion\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"bg-gray-700 rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors\">\n                    <div className=\"relative mb-3\">\n                      <div className=\"w-full h-24 bg-gradient-to-br from-amber-400 to-orange-500 rounded-lg\"></div>\n                    </div>\n                    <div className=\"space-y-2\">\n                      <div className=\"text-white font-medium text-sm\">\n                        morning_coffee.jpg\n                      </div>\n                      <div className=\"text-gray-300 text-xs leading-relaxed\">\n                        Perfect morning coffee setup with natural lighting\n                      </div>\n                      <div className=\"flex justify-between items-center\">\n                        <span className=\"text-gray-400 text-xs\">\n                          Oct 27, 2024\n                        </span>\n                        <span className=\"px-2 py-1 bg-gray-500 text-white text-xs rounded-full\">\n                          Unused\n                        </span>\n                      </div>\n                      <div className=\"flex gap-1 flex-wrap\">\n                        <span className=\"px-2 py-1 bg-gray-600 text-gray-200 text-xs rounded-full\">\n                          coffee\n                        </span>\n                        <span className=\"px-2 py-1 bg-gray-600 text-gray-200 text-xs rounded-full\">\n                          morning\n                        </span>\n                      </div>\n                      <div className=\"inline-block px-2 py-1 bg-blue-500 text-white text-xs rounded-full\">\n                        Lifestyle\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"flex-1 space-y-6\">\n              <h3 className=\"text-2xl font-bold text-white\">\n                Start with Your Photos\n              </h3>\n              <p className=\"text-gray-300 leading-relaxed\">\n                Upload any photo and watch AI instantly match it with trending\n                topics. Your visuals drive the content, not the other way\n                around.\n              </p>\n            </div>\n          </motion.div>\n\n          {/* Screenshot 2: Persona Selection */}\n          <motion.div\n            className=\"screenshot-item reverse\"\n            variants={itemVariants}\n          >\n            <div className=\"screenshot-image\">\n              <div className=\"screenshot-content\">\n                <div className=\"demo-persona-manager-compact\">\n                  <div className=\"persona-grid\">\n                    <div className=\"persona-card active\">\n                      <div className=\"persona-card-avatar fiona\">F</div>\n                      <h4>Fashion Fiona</h4>\n                      <p>ENFP • Fashion & Lifestyle</p>\n                      <div className=\"persona-knowledge-preview\">\n                        <div className=\"knowledge-chip-mini active\">\n                          Fashion\n                        </div>\n                        <div className=\"knowledge-chip-mini active\">Style</div>\n                        <div className=\"knowledge-chip-mini active\">Trends</div>\n                      </div>\n                    </div>\n\n                    <div className=\"persona-card\">\n                      <div className=\"persona-card-avatar marcus\">M</div>\n                      <h4>Chef Marcus</h4>\n                      <p>ISFJ • Culinary Expert</p>\n                      <div className=\"persona-knowledge-preview\">\n                        <div className=\"knowledge-chip-mini\">Recipes</div>\n                        <div className=\"knowledge-chip-mini\">Cooking</div>\n                        <div className=\"knowledge-chip-mini\">Food</div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"persona-detail-summary\">\n                    <h4\n                      style={{\n                        color: \"var(--text-primary)\",\n                        marginBottom: \"12px\",\n                        fontSize: \"16px\",\n                      }}\n                    >\n                      Selected: Fashion Fiona\n                    </h4>\n                    <p\n                      style={{\n                        color: \"var(--text-secondary)\",\n                        fontSize: \"14px\",\n                        lineHeight: \"1.5\",\n                        marginBottom: \"16px\",\n                      }}\n                    >\n                      A fashion-forward influencer who stays ahead of trends and\n                      creates authentic style content. Reads fashion feeds and\n                      writes with trendy enthusiasm.\n                    </p>\n                    <div className=\"knowledge-sources-summary\">\n                      <div className=\"knowledge-chips\">\n                        <div className=\"knowledge-chip active\">Vogue RSS</div>\n                        <div className=\"knowledge-chip active\">\n                          Fashion Week\n                        </div>\n                        <div className=\"knowledge-chip active\">Style Blogs</div>\n                        <div className=\"knowledge-chip\">Beauty Tips</div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"screenshot-info\">\n              <h3>Personas with Custom Knowledge</h3>\n              <p>\n                Each persona reads different sources and writes in their unique\n                style. Fashion Fiona follows fashion feeds, while Foodie Fred\n                reads culinary blogs.\n              </p>\n            </div>\n          </motion.div>\n\n          {/* Screenshot 3: AI Generation Process */}\n          <motion.div className=\"screenshot-item\" variants={itemVariants}>\n            <div className=\"screenshot-image\">\n              <div className=\"screenshot-content\">\n                <div className=\"demo-content-generation-compact\">\n                  <div className=\"generation-process\">\n                    <div className=\"process-steps\">\n                      <div className=\"step-item active\">\n                        <div className=\"step-icon\">📸</div>\n                        <span>{tContentGeneration(\"steps.photoAnalysis\")}</span>\n                      </div>\n                      <div className=\"step-arrow\">→</div>\n                      <div className=\"step-item active\">\n                        <div className=\"step-icon\">📈</div>\n                        <span>{tContentGeneration(\"steps.trendMatch\")}</span>\n                      </div>\n                      <div className=\"step-arrow\">→</div>\n                      <div className=\"step-item active\">\n                        <div className=\"step-icon\">✨</div>\n                        <span>{tContentGeneration(\"steps.generate\")}</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"generated-content-preview\">\n                    <div className=\"content-example\">\n                      <div className=\"content-header\">\n                        <div className=\"persona-badge\">\n                          {tContentGeneration(\"badges.fashionFiona\")}\n                        </div>\n                        <div className=\"trend-badge\">\n                          {tContentGeneration(\"badges.autumnTrends\")}\n                        </div>\n                      </div>\n                      <div className=\"content-body\">\n                        <p>{tContentGeneration(\"sampleContent\")}</p>\n                      </div>\n                      <div className=\"content-platforms\">\n                        <span className=\"platform-icon\">📱</span>\n                        <span className=\"platform-icon\">📷</span>\n                        <span className=\"platform-icon\">🐦</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"screenshot-info\">\n              <h3>{tScreenshots(\"smartContentGeneration.title\")}</h3>\n              <p>{tScreenshots(\"smartContentGeneration.description\")}</p>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,eAAe,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IACrC,MAAM,qBAAqB,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC3C,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAE/D,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;QAAiB,KAAK;kBACvC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,aAAa;;;;;;sCAEhB,8OAAC;4BAAE,WAAU;sCACV,aAAa;;;;;;;;;;;;8BAIlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU;oBACV,SAAQ;oBACR,SAAS,WAAW,YAAY;;sCAGhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAoC;;;;;;0EAGlD,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAI1C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAqE;;;;;;0EAGpF,8OAAC;gEAAI,WAAU;0EAA0G;;;;;;0EAGzH,8OAAC;gEAAI,WAAU;0EAA0G;;;;;;0EAGzH,8OAAC;gEAAI,WAAU;0EAA0G;;;;;;0EAGzH,8OAAC;gEAAI,WAAU;0EAA0G;;;;;;;;;;;;;;;;;;0DAO7H,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEAAI,WAAU;kFAA6I;;;;;;;;;;;;0EAI9J,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAiC;;;;;;kFAGhD,8OAAC;wEAAI,WAAU;kFAAwC;;;;;;kFAIvD,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAwB;;;;;;0FAGxC,8OAAC;gFAAK,WAAU;0FAAyD;;;;;;;;;;;;kFAI3E,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAA2D;;;;;;0FAG3E,8OAAC;gFAAK,WAAU;0FAA2D;;;;;;0FAG3E,8OAAC;gFAAK,WAAU;0FAA2D;;;;;;;;;;;;kFAI7E,8OAAC;wEAAI,WAAU;kFAAoE;;;;;;;;;;;;;;;;;;kEAMvF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;;;;;;;;;;0EAEjB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAiC;;;;;;kFAGhD,8OAAC;wEAAI,WAAU;kFAAwC;;;;;;kFAGvD,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAwB;;;;;;0FAGxC,8OAAC;gFAAK,WAAU;0FAAwD;;;;;;;;;;;;kFAI1E,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAA2D;;;;;;0FAG3E,8OAAC;gFAAK,WAAU;0FAA2D;;;;;;;;;;;;kFAI7E,8OAAC;wEAAI,WAAU;kFAAqE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ9F,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAG9C,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCASjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAA4B;;;;;;8EAC3C,8OAAC;8EAAG;;;;;;8EACJ,8OAAC;8EAAE;;;;;;8EACH,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAA6B;;;;;;sFAG5C,8OAAC;4EAAI,WAAU;sFAA6B;;;;;;sFAC5C,8OAAC;4EAAI,WAAU;sFAA6B;;;;;;;;;;;;;;;;;;sEAIhD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAA6B;;;;;;8EAC5C,8OAAC;8EAAG;;;;;;8EACJ,8OAAC;8EAAE;;;;;;8EACH,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAAsB;;;;;;sFACrC,8OAAC;4EAAI,WAAU;sFAAsB;;;;;;sFACrC,8OAAC;4EAAI,WAAU;sFAAsB;;;;;;;;;;;;;;;;;;;;;;;;8DAK3C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,OAAO;gEACL,OAAO;gEACP,cAAc;gEACd,UAAU;4DACZ;sEACD;;;;;;sEAGD,8OAAC;4DACC,OAAO;gEACL,OAAO;gEACP,UAAU;gEACV,YAAY;gEACZ,cAAc;4DAChB;sEACD;;;;;;sEAKD,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAwB;;;;;;kFACvC,8OAAC;wEAAI,WAAU;kFAAwB;;;;;;kFAGvC,8OAAC;wEAAI,WAAU;kFAAwB;;;;;;kFACvC,8OAAC;wEAAI,WAAU;kFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;sCASP,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,WAAU;4BAAkB,UAAU;;8CAChD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAY;;;;;;kFAC3B,8OAAC;kFAAM,mBAAmB;;;;;;;;;;;;0EAE5B,8OAAC;gEAAI,WAAU;0EAAa;;;;;;0EAC5B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAY;;;;;;kFAC3B,8OAAC;kFAAM,mBAAmB;;;;;;;;;;;;0EAE5B,8OAAC;gEAAI,WAAU;0EAAa;;;;;;0EAC5B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAY;;;;;;kFAC3B,8OAAC;kFAAM,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;8DAKhC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,mBAAmB;;;;;;kFAEtB,8OAAC;wEAAI,WAAU;kFACZ,mBAAmB;;;;;;;;;;;;0EAGxB,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;8EAAG,mBAAmB;;;;;;;;;;;0EAEzB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAI,aAAa;;;;;;sDAClB,8OAAC;sDAAG,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/B", "debugId": null}}, {"offset": {"line": 1671, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/sections/CTASection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"framer-motion\";\nimport { useRef, useState } from \"react\";\nimport { useTranslations } from \"next-intl\";\n\nexport default function CTASection() {\n  const tCTA = useTranslations(\"CTA\");\n  const tCommon = useTranslations(\"Common\");\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" });\n  const [email, setEmail] = useState(\"\");\n  const [isSubmitted, setIsSubmitted] = useState(false);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (email) {\n      // Simulate form submission\n      setIsSubmitted(true);\n      setEmail(\"\");\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0, y: 50 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.8,\n        ease: \"easeOut\" as const,\n      },\n    },\n  };\n\n  return (\n    <section className=\"py-24 text-center relative bg-gray-900\" ref={ref}>\n      <div className=\"max-w-4xl mx-auto px-5\">\n        <motion.div\n          className=\"space-y-8\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate={isInView ? \"visible\" : \"hidden\"}\n        >\n          <h2 className=\"text-4xl lg:text-5xl font-black text-white leading-tight\">\n            {tCTA(\"title\")}\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed\">\n            {tCTA(\"subtitle\")}\n          </p>\n\n          {!isSubmitted ? (\n            <form className=\"mt-12\" onSubmit={handleSubmit}>\n              <div className=\"flex flex-col sm:flex-row gap-4 max-w-lg mx-auto\">\n                <input\n                  type=\"email\"\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                  placeholder={tCTA(\"emailPlaceholder\")}\n                  className=\"flex-1 px-6 py-4 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500 focus:ring-2 focus:ring-red-500/20 transition-all duration-300\"\n                  required\n                />\n                <button\n                  type=\"submit\"\n                  className=\"bg-gradient-to-r from-red-500 to-purple-600 text-white px-8 py-4 rounded-lg font-semibold text-base hover:transform hover:-translate-y-1 transition-all duration-300 shadow-lg hover:shadow-red-500/30 whitespace-nowrap\"\n                >\n                  {tCommon(\"getEarlyAccess\")}\n                </button>\n              </div>\n              <p className=\"text-gray-400 text-sm mt-6 max-w-md mx-auto\">\n                {tCTA(\"note\")}\n              </p>\n            </form>\n          ) : (\n            <div className=\"bg-green-500/10 border border-green-500/20 rounded-xl p-8 max-w-md mx-auto\">\n              <h3 className=\"text-xl font-bold text-green-400 mb-3\">\n                {tCTA(\"successTitle\")}\n              </h3>\n              <p className=\"text-gray-300\">{tCTA(\"successMessage\")}</p>\n            </div>\n          )}\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,OAAO,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC7B,MAAM,UAAU,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAC/D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,OAAO;YACT,2BAA2B;YAC3B,eAAe;YACf,SAAS;QACX;IACF;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;QAAyC,KAAK;kBAC/D,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;gBACV,SAAQ;gBACR,SAAS,WAAW,YAAY;;kCAEhC,8OAAC;wBAAG,WAAU;kCACX,KAAK;;;;;;kCAER,8OAAC;wBAAE,WAAU;kCACV,KAAK;;;;;;oBAGP,CAAC,4BACA,8OAAC;wBAAK,WAAU;wBAAQ,UAAU;;0CAChC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,aAAa,KAAK;wCAClB,WAAU;wCACV,QAAQ;;;;;;kDAEV,8OAAC;wCACC,MAAK;wCACL,WAAU;kDAET,QAAQ;;;;;;;;;;;;0CAGb,8OAAC;gCAAE,WAAU;0CACV,KAAK;;;;;;;;;;;6CAIV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,KAAK;;;;;;0CAER,8OAAC;gCAAE,WAAU;0CAAiB,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 1837, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/ScrollToCTA.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\n\nexport default function ScrollToCTA() {\n  useEffect(() => {\n    const handleScrollToCTA = (e: Event) => {\n      const target = e.target as HTMLElement;\n      \n      // Check if the clicked element is a CTA button (but not in a form)\n      if (\n        target.classList.contains('btn-primary') &&\n        (target.textContent?.includes('Google') || target.textContent?.includes('Early Access')) &&\n        !target.closest('form')\n      ) {\n        e.preventDefault();\n        \n        // Find the CTA section and scroll to it\n        const ctaSection = document.querySelector('.cta');\n        if (ctaSection) {\n          ctaSection.scrollIntoView({ \n            behavior: 'smooth',\n            block: 'start'\n          });\n        }\n      }\n    };\n\n    // Add event listener to document\n    document.addEventListener('click', handleScrollToCTA);\n\n    // Cleanup\n    return () => {\n      document.removeEventListener('click', handleScrollToCTA);\n    };\n  }, []);\n\n  return null; // This component doesn't render anything\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIe,SAAS;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB,CAAC;YACzB,MAAM,SAAS,EAAE,MAAM;YAEvB,mEAAmE;YACnE,IACE,OAAO,SAAS,CAAC,QAAQ,CAAC,kBAC1B,CAAC,OAAO,WAAW,EAAE,SAAS,aAAa,OAAO,WAAW,EAAE,SAAS,eAAe,KACvF,CAAC,OAAO,OAAO,CAAC,SAChB;gBACA,EAAE,cAAc;gBAEhB,wCAAwC;gBACxC,MAAM,aAAa,SAAS,aAAa,CAAC;gBAC1C,IAAI,YAAY;oBACd,WAAW,cAAc,CAAC;wBACxB,UAAU;wBACV,OAAO;oBACT;gBACF;YACF;QACF;QAEA,iCAAiC;QACjC,SAAS,gBAAgB,CAAC,SAAS;QAEnC,UAAU;QACV,OAAO;YACL,SAAS,mBAAmB,CAAC,SAAS;QACxC;IACF,GAAG,EAAE;IAEL,OAAO,MAAM,yCAAyC;AACxD", "debugId": null}}]}