import { useTranslations } from "next-intl";

export default function FooterSection() {
  const tFooter = useTranslations("Footer");
  const tNav = useTranslations("Navigation");

  return (
    <footer className="py-16 bg-gray-900 border-t border-gray-800 md:py-12">
      <div className="max-w-6xl mx-auto px-5">
        <div className="flex justify-between items-center mb-8 flex-col md:flex-row gap-8 md:gap-0">
          <div className="flex flex-col gap-2 text-center md:text-left">
            <div className="flex items-center gap-3 justify-center md:justify-start">
              <div className="w-8 h-8 relative">
                <div className="relative w-full h-full">
                  <div className="absolute inset-0 bg-gradient-to-br from-red-500 to-purple-600 rounded-lg opacity-80"></div>
                  <div className="absolute inset-0.5 bg-gradient-to-br from-purple-600 to-blue-500 rounded-lg opacity-60"></div>
                  <div className="absolute inset-1 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-lg opacity-40"></div>
                </div>
              </div>
              <span className="text-xl font-bold text-white">PersonaRoll</span>
            </div>
            <p className="text-gray-400 text-sm m-0">
              Authentic Voices, Infinite Stories
            </p>
          </div>
          <div className="flex gap-8 flex-wrap justify-center md:justify-end">
            <a
              href="#"
              className="text-gray-300 text-sm hover:text-red-500 transition-colors duration-300"
            >
              {tNav("privacy")}
            </a>
            <a
              href="#"
              className="text-gray-300 text-sm hover:text-red-500 transition-colors duration-300"
            >
              {tNav("terms")}
            </a>
            <a
              href="#"
              className="text-gray-300 text-sm hover:text-red-500 transition-colors duration-300"
            >
              {tNav("contact")}
            </a>
            <a
              href="#"
              className="text-gray-300 text-sm hover:text-red-500 transition-colors duration-300"
            >
              {tNav("twitter")}
            </a>
          </div>
        </div>

        {/* Footer Bottom */}
        <div className="text-center pt-8 border-t border-gray-800">
          <p className="text-gray-400 text-sm m-0">{tFooter("copyright")}</p>
        </div>
      </div>
    </footer>
  );
}
