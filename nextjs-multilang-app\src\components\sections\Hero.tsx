"use client";

import { motion } from "framer-motion";
import { useTranslations } from "next-intl";

export default function Hero() {
  const tHero = useTranslations("Hero");
  const tCommon = useTranslations("Common");
  const tPersonas = useTranslations("Personas");
  const tContent = useTranslations("ContentGeneration");
  return (
    <section className="hero">
      <div className="hero-content">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="hero-badge">{tHero("badge")}</div>
          <h1 className="hero-title">
            {tHero("title")}
            <br />
            <span className="gradient-text">{tHero("subtitle")}</span>
          </h1>
          <p className="hero-subtitle">{tHero("description")}</p>
          <div className="hero-actions">
            <button className="btn-primary btn-large">
              {tCommon("getEarlyAccess")}
            </button>
            <button className="btn-secondary btn-large">
              {tCommon("watchDemo")}
            </button>
          </div>
          <div className="hero-stats">
            <div className="stat">
              <span className="stat-number">{tHero("stats.ai")}</span>
              <span className="stat-label">{tHero("stats.aiLabel")}</span>
            </div>
            <div className="stat">
              <span className="stat-number">{tHero("stats.live")}</span>
              <span className="stat-label">{tHero("stats.liveLabel")}</span>
            </div>
            <div className="stat">
              <span className="stat-number">{tHero("stats.infinite")}</span>
              <span className="stat-label">{tHero("stats.infiniteLabel")}</span>
            </div>
          </div>
        </motion.div>
      </div>

      <div className="hero-visual">
        <div className="hero-mockup">
          <motion.div
            className="floating-card card-1"
            animate={{
              y: [0, -20, 0],
              rotate: [0, 2, 0],
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          >
            <div className="persona-preview">
              <img
                src="data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='30' cy='30' r='30' fill='url(%23gradient1)'/%3E%3Cdefs%3E%3ClinearGradient id='gradient1' x1='0' y1='0' x2='60' y2='60'%3E%3Cstop stop-color='%23ff2e4d'/%3E%3Cstop offset='1' stop-color='%237c3aed'/%3E%3C/linearGradient%3E%3C/defs%3E%3C/svg%3E"
                alt={tPersonas("fashionQianqian.name")}
              />
              <div>
                <h4>{tPersonas("fashionQianqian.name")}</h4>
                <p>{tPersonas("fashionQianqian.type")}</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="floating-card card-2"
            animate={{
              y: [0, -15, 0],
              rotate: [0, -1, 0],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2,
            }}
          >
            <div className="platform-icons">
              <div className="source-icon">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" />
                  <polyline points="22,6 12,13 2,6" />
                </svg>
              </div>
              <div className="source-icon">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                  <polyline points="14,2 14,8 20,8" />
                  <line x1="16" y1="13" x2="8" y2="13" />
                  <line x1="16" y1="17" x2="8" y2="17" />
                  <polyline points="10,9 9,9 8,9" />
                </svg>
              </div>
              <div className="source-icon">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" />
                  <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" />
                </svg>
              </div>
              <div className="source-icon">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
                  <line x1="16" y1="2" x2="16" y2="6" />
                  <line x1="8" y1="2" x2="8" y2="6" />
                  <line x1="3" y1="10" x2="21" y2="10" />
                  <path d="M8 14h.01" />
                  <path d="M12 14h.01" />
                  <path d="M16 14h.01" />
                  <path d="M8 18h.01" />
                  <path d="M12 18h.01" />
                </svg>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="floating-card card-3"
            animate={{
              y: [0, -25, 0],
              rotate: [0, 1, 0],
            }}
            transition={{
              duration: 7,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4,
            }}
          >
            <div className="content-preview">
              <div className="preview-image">
                <svg width="80" height="60" viewBox="0 0 80 60" fill="none">
                  <defs>
                    <linearGradient
                      id="autumnBg"
                      x1="0%"
                      y1="0%"
                      x2="100%"
                      y2="100%"
                    >
                      <stop offset="0%" style={{ stopColor: "#FFA07A" }} />
                      <stop offset="50%" style={{ stopColor: "#CD853F" }} />
                      <stop offset="100%" style={{ stopColor: "#8B4513" }} />
                    </linearGradient>
                    <linearGradient
                      id="sweaterGrad"
                      x1="0%"
                      y1="0%"
                      x2="100%"
                      y2="100%"
                    >
                      <stop offset="0%" style={{ stopColor: "#D2691E" }} />
                      <stop offset="100%" style={{ stopColor: "#A0522D" }} />
                    </linearGradient>
                  </defs>
                  <rect width="80" height="60" fill="url(#autumnBg)" rx="6" />

                  <g opacity="0.3">
                    <path d="M15 12 Q18 8 21 12 Q18 16 15 12" fill="#8B0000" />
                    <path d="M62 15 Q65 11 68 15 Q65 19 62 15" fill="#FF6347" />
                    <path d="M8 35 Q11 31 14 35 Q11 39 8 35" fill="#CD853F" />
                    <path d="M70 45 Q73 41 76 45 Q73 49 70 45" fill="#B22222" />
                    <path d="M25 50 Q28 46 31 50 Q28 54 25 50" fill="#FF8C00" />
                  </g>

                  <ellipse
                    cx="40"
                    cy="35"
                    rx="18"
                    ry="20"
                    fill="url(#sweaterGrad)"
                  />
                  <ellipse cx="40" cy="32" rx="15" ry="16" fill="#DEB887" />

                  <line
                    x1="30"
                    y1="25"
                    x2="50"
                    y2="25"
                    stroke="#CD853F"
                    strokeWidth="0.5"
                  />
                  <line
                    x1="30"
                    y1="30"
                    x2="50"
                    y2="30"
                    stroke="#CD853F"
                    strokeWidth="0.5"
                  />
                  <line
                    x1="30"
                    y1="35"
                    x2="50"
                    y2="35"
                    stroke="#CD853F"
                    strokeWidth="0.5"
                  />

                  <ellipse cx="40" cy="18" rx="12" ry="4" fill="#8B0000" />
                  <rect x="48" y="15" width="3" height="12" fill="#8B0000" />
                  <rect x="52" y="17" width="2" height="8" fill="#CD853F" />

                  <rect
                    x="32"
                    y="48"
                    width="16"
                    height="10"
                    fill="#4682B4"
                    rx="2"
                  />
                  <line
                    x1="34"
                    y1="50"
                    x2="34"
                    y2="56"
                    stroke="#191970"
                    strokeWidth="0.5"
                  />
                  <line
                    x1="46"
                    y1="50"
                    x2="46"
                    y2="56"
                    stroke="#191970"
                    strokeWidth="0.5"
                  />

                  <ellipse cx="36" cy="57" rx="4" ry="2" fill="#654321" />
                  <ellipse cx="44" cy="57" rx="4" ry="2" fill="#654321" />

                  <circle cx="20" cy="20" r="1" fill="#FFD700" opacity="0.8" />
                  <circle cx="60" cy="25" r="1" fill="#FFD700" opacity="0.8" />
                  <circle cx="15" cy="45" r="1" fill="#FFD700" opacity="0.6" />
                  <circle cx="65" cy="50" r="1" fill="#FFD700" opacity="0.6" />
                </svg>
              </div>
              <p>&quot;{tContent("previewContent")}&quot;</p>
              <span className="preview-meta">{tContent("aiGenerated")}</span>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
