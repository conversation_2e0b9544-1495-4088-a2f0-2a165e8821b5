{"version": 3, "sources": [], "sections": [{"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/i18n/request.ts"], "sourcesContent": ["import { getRequestConfig } from \"next-intl/server\";\nimport { routing } from \"./routing\";\n\nexport default getRequestConfig(async ({ requestLocale }) => {\n  // This typically corresponds to the `[locale]` segment\n  let locale = await requestLocale;\n\n  // Ensure that a valid locale is used\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  if (!locale || !routing.locales.includes(locale as any)) {\n    locale = routing.defaultLocale;\n  }\n\n  return {\n    locale,\n    messages: (await import(`../../messages/${locale}.json`)).default,\n  };\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAEe,CAAA,GAAA,kQAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,aAAa,EAAE;IACtD,uDAAuD;IACvD,IAAI,SAAS,MAAM;IAEnB,qCAAqC;IACrC,8DAA8D;IAC9D,IAAI,CAAC,UAAU,CAAC,8HAAA,CAAA,UAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAgB;QACvD,SAAS,8HAAA,CAAA,UAAO,CAAC,aAAa;IAChC;IAEA,OAAO;QACL;QACA,UAAU,CAAC;;;;;;;;;;;;;kBAAa,CAAC,eAAe,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IACnE;AACF"}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/i18n/routing.ts"], "sourcesContent": ["import { defineRouting } from \"next-intl/routing\";\nimport { createNavigation } from \"next-intl/navigation\";\n\nexport const routing = defineRouting({\n  // A list of all locales that are supported\n  locales: [\"en\", \"zh\", \"ja\"],\n\n  // Used when no locale matches\n  defaultLocale: \"en\",\n\n  // The `pathnames` object holds pairs of internal and\n  // external paths. Based on the locale, the external\n  // paths are rewritten to the shared, internal ones.\n  pathnames: {\n    // If all locales use the same pathname, a single\n    // external path can be provided for all locales\n    \"/\": \"/\",\n    \"/about\": {\n      en: \"/about\",\n      zh: \"/about\",\n      ja: \"/about\",\n    },\n  },\n});\n\n// Lightweight wrappers around Next.js' navigation APIs\n// that will consider the routing configuration\nexport const { Link, redirect, usePathname, useRouter } =\n  createNavigation(routing);\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,MAAM,UAAU,CAAA,GAAA,0OAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,2CAA2C;IAC3C,SAAS;QAAC;QAAM;QAAM;KAAK;IAE3B,8BAA8B;IAC9B,eAAe;IAEf,qDAAqD;IACrD,oDAAoD;IACpD,oDAAoD;IACpD,WAAW;QACT,iDAAiD;QACjD,gDAAgD;QAChD,KAAK;QACL,UAAU;YACR,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;AACF;AAIO,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,GACrD,CAAA,GAAA,sQAAA,CAAA,mBAAgB,AAAD,EAAE"}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import createMiddleware from 'next-intl/middleware';\nimport {routing} from './i18n/routing';\n \nexport default createMiddleware(routing);\n \nexport const config = {\n  // Match only internationalized pathnames\n  matcher: ['/', '/(zh|en|ja)/:path*']\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;uCAEe,CAAA,GAAA,8LAAA,CAAA,UAAgB,AAAD,EAAE,8HAAA,CAAA,UAAO;AAEhC,MAAM,SAAS;IACpB,yCAAyC;IACzC,SAAS;QAAC;QAAK;KAAqB;AACtC"}}]}