{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/sections/Hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/Hero.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/Hero.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/sections/Hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/Hero.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/Hero.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/sections/FeaturesSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/FeaturesSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/FeaturesSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+S,GAC5U,6EACA", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/sections/FeaturesSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/FeaturesSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/FeaturesSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/sections/ScreenshotsSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/ScreenshotsSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/ScreenshotsSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkT,GAC/U,gFACA", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/sections/ScreenshotsSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/ScreenshotsSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/ScreenshotsSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/sections/CTASection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/CTASection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/CTASection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0S,GACvU,wEACA", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/sections/CTASection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/CTASection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/CTASection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsR,GACnT,oDACA", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/sections/FooterSection.tsx"], "sourcesContent": ["import { useTranslations } from \"next-intl\";\n\nexport default function FooterSection() {\n  const tFooter = useTranslations(\"Footer\");\n  const tNav = useTranslations(\"Navigation\");\n\n  return (\n    <footer className=\"py-16 bg-bg-primary md:py-12\">\n      <div className=\"max-w-6xl mx-auto px-5\">\n        <div className=\"flex justify-between items-center mb-8 flex-col md:flex-row gap-8 md:gap-0\">\n          <div className=\"flex flex-col gap-2 text-center md:text-left\">\n            <div className=\"flex items-center gap-3 justify-center md:justify-start\">\n              <div className=\"w-8 h-8 relative\">\n                <div className=\"relative w-full h-full\">\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-red-500 to-purple-600 rounded-lg opacity-80\"></div>\n                  <div className=\"absolute inset-0.5 bg-gradient-to-br from-purple-600 to-blue-500 rounded-lg opacity-60\"></div>\n                  <div className=\"absolute inset-1 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-lg opacity-40\"></div>\n                </div>\n              </div>\n              <span className=\"text-xl font-bold text-white\">PersonaRoll</span>\n            </div>\n            <p className=\"text-gray-400 text-sm m-0\">\n              Authentic Voices, Infinite Stories\n            </p>\n          </div>\n          <div className=\"flex gap-8 flex-wrap justify-center md:justify-end\">\n            <a\n              href=\"#\"\n              className=\"text-gray-300 text-sm hover:text-red-500 transition-colors duration-300\"\n            >\n              {tNav(\"privacy\")}\n            </a>\n            <a\n              href=\"#\"\n              className=\"text-gray-300 text-sm hover:text-red-500 transition-colors duration-300\"\n            >\n              {tNav(\"terms\")}\n            </a>\n            <a\n              href=\"#\"\n              className=\"text-gray-300 text-sm hover:text-red-500 transition-colors duration-300\"\n            >\n              {tNav(\"contact\")}\n            </a>\n            <a\n              href=\"#\"\n              className=\"text-gray-300 text-sm hover:text-red-500 transition-colors duration-300\"\n            >\n              {tNav(\"twitter\")}\n            </a>\n          </div>\n        </div>\n        {/* Footer Bottom */}\n        <div className=\"text-center pt-8 border-t border-border\">\n          <p className=\"text-gray-400 text-sm m-0\">{tFooter(\"copyright\")}</p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,MAAM,UAAU,CAAA,GAAA,8OAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,MAAM,OAAO,CAAA,GAAA,8OAAA,CAAA,kBAAe,AAAD,EAAE;IAE7B,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;sDAGnB,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;;8CAEjD,8OAAC;oCAAE,WAAU;8CAA4B;;;;;;;;;;;;sCAI3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,WAAU;8CAET,KAAK;;;;;;8CAER,8OAAC;oCACC,MAAK;oCACL,WAAU;8CAET,KAAK;;;;;;8CAER,8OAAC;oCACC,MAAK;oCACL,WAAU;8CAET,KAAK;;;;;;8CAER,8OAAC;oCACC,MAAK;oCACL,WAAU;8CAET,KAAK;;;;;;;;;;;;;;;;;;8BAKZ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAA6B,QAAQ;;;;;;;;;;;;;;;;;;;;;;AAK5D", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/ScrollToCTA.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ScrollToCTA.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ScrollToCTA.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/ScrollToCTA.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ScrollToCTA.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ScrollToCTA.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/app/%5Blocale%5D/page.tsx"], "sourcesContent": ["import Navigation from \"@/components/Navigation\";\r\nimport Hero from \"@/components/sections/Hero\";\r\nimport FeaturesSection from \"@/components/sections/FeaturesSection\";\r\nimport ScreenshotsSection from \"@/components/sections/ScreenshotsSection\";\r\nimport CTASection from \"@/components/sections/CTASection\";\r\nimport FooterSection from \"@/components/sections/FooterSection\";\r\nimport ScrollToCTA from \"@/components/ScrollToCTA\";\r\n\r\nexport default function HomePage() {\r\n  return (\r\n    <div className=\"min-h-screen\">\r\n      <ScrollToCTA />\r\n      <Navigation />\r\n      <Hero />\r\n      <FeaturesSection />\r\n      <ScreenshotsSection />\r\n      <CTASection />\r\n      <FooterSection />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,iIAAA,CAAA,UAAW;;;;;0BACZ,8OAAC,gIAAA,CAAA,UAAU;;;;;0BACX,8OAAC,sIAAA,CAAA,UAAI;;;;;0BACL,8OAAC,iJAAA,CAAA,UAAe;;;;;0BAChB,8OAAC,oJAAA,CAAA,UAAkB;;;;;0BACnB,8OAAC,4IAAA,CAAA,UAAU;;;;;0BACX,8OAAC,+IAAA,CAAA,UAAa;;;;;;;;;;;AAGpB", "debugId": null}}]}